#include "logindialog.h"

LoginDialog::LoginDialog(QWidget *parent) : QDialog(parent)
{
    // --- 1. 窗口级别的基本设置 ---
    this->setWindowTitle("用户登录");
    this->setFixedSize(400,320);
    this->setFont(QFont("Microsoft YaHei", 14));

    // --- 2. “账号”功能区的控件创建与配置 ---
    usernameLabel = new QLabel("账号",this);
    usernameLabel->setAlignment(Qt::AlignRight | Qt::AlignVCenter);
    usernameEdit = new QLineEdit(this);
    usernameEdit->setPlaceholderText("请输入账号");
    usernameEdit->setMinimumHeight(32);

    // --- 3. “密码”功能区的控件创建与配置 ---
    passwordLabel = new QLabel("密码",this);
    passwordLabel->setAlignment(Qt::AlignRight | Qt::AlignVCenter);
    passwordEdit = new QLineEdit(this);
    passwordEdit->setPlaceholderText("请输入密码");
    passwordEdit->setEchoMode(QLineEdit::Password);
    passwordEdit->setMinimumHeight(32);

    // --- 4. “按钮”功能区的控件创建 ---
    loginButton = new QPushButton("登录",this);
    loginButton->setFixedSize(80, 36);
    cancelButton = new QPushButton("取消",this);
    cancelButton->setFixedSize(80, 36);

    // --- 5. 布局管理：使用布局管理器智能地排列控件 ---
    buttonLayout = new QHBoxLayout;
    buttonLayout->addStretch();
    buttonLayout->addWidget(loginButton);
    buttonLayout->addWidget(cancelButton);
    buttonLayout->addStretch();

    grid = new QGridLayout(this);
    grid->addWidget(usernameLabel,0,0);
    grid->addWidget(usernameEdit,0,1);
    grid->addWidget(passwordLabel,1,0);
    grid->addWidget(passwordEdit,1,1);
    grid->addLayout(buttonLayout,2,0,1,2);

    // --- 6. 信号与槽的连接 ---
    connect(loginButton, &QPushButton::clicked, this, &LoginDialog::handleLogin);
    connect(cancelButton, &QPushButton::clicked, this, &LoginDialog::reject);
}

void LoginDialog::handleLogin() {
    // 登录逻辑实现
    QString username = usernameEdit->text();
    QString password = passwordEdit->text();

    // 这里可以进行用户名和密码验证等操作
    if (username == "admin" && password == "1234") {
        accept();  // 登录成功，关闭登录窗口
    } else {
        QMessageBox::warning(this, "错误", "用户名或密码错误");
    }
}

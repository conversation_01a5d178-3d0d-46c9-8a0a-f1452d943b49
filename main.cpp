#include "mainwindow.h"
#include "logindialog.h"

#include <QApplication>
#include <QNetworkProxy>

int main(int argc, char *argv[])
{
    QApplication a(argc, argv);

    // 设置窗口图标
    a.setWindowIcon(QIcon("images/title.ico"));

    // 设置全局网络代理
    QNetworkProxy proxy;
    proxy.setType(QNetworkProxy::NoProxy);  // 如果需要代理，请设置为适当的代理类型
    QNetworkProxy::setApplicationProxy(proxy);

    // 创建并显示登录对话框
    LoginDialog login;
    if (login.exec() == QDialog::Accepted) {
        // 登录成功，创建主窗口
        MainWindow w;
        w.setWindowState(Qt::WindowMaximized);  // 设置窗口最大化
        w.show();
        return a.exec();  // 启动事件循环
    } else {
        // 用户取消登录，退出程序
        return 0;
    }
}


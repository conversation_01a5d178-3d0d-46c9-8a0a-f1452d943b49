#ifndef LOGINDIALOG_H
#define LOGINDIALOG_H

#include <QObject>
#include <QDialog>
#include <QLabel>
#include <QLineEdit>
#include <QPushButton>
#include <QGridLayout>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QMessageBox>
#include <QFont>
#include <QSqlQuery>
#include <QSqlError>
#include <QFormLayout>

class LoginDialog : public QDialog
{
    Q_OBJECT

public:
    explicit LoginDialog(QWidget *parent = nullptr);

private slots:
    void handleLogin();

private:
    QLabel* usernameLabel;
    QLineEdit* usernameEdit;
    QLabel* passwordLabel;
    QLineEdit* passwordEdit;
    QPushButton* loginButton;
    QPushButton* cancelButton;
    QHBoxLayout* buttonLayout;
    QGridLayout* grid;
};

#endif // LOGINDIALOG_H
